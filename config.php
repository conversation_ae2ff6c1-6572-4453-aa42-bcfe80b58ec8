<?php
/**
 * Configuracion de la base de datos para el sistema de agendamiento
 * Servidor: agendacotte.com
 */

// Configuracion de la base de datos
define('DB_HOST', '**************');
define('DB_NAME', 'agendamiento');
define('DB_USER', 'root');
define('DB_PASS', 'Q7Mc9O9wEbs67fmK5G');
define('DB_CHARSET', 'utf8mb4');

// Configuracion de zona horaria
date_default_timezone_set('America/Bogota');

// Configuracion de errores (cambiar a false en produccion)
define('DEBUG_MODE', true);

if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 0); // Cambiado a 0 para evitar output no-JSON
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Manejador de errores fatal para asegurar respuesta JSON
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        // Limpiar cualquier output previo
        if (ob_get_level()) {
            ob_clean();
        }

        http_response_code(500);
        header('Content-Type: application/json; charset=utf-8');

        $response = [
            'success' => false,
            'message' => 'Error interno del servidor'
        ];

        if (DEBUG_MODE) {
            $response['error'] = $error['message'];
            $response['file'] = $error['file'];
            $response['line'] = $error['line'];
        }

        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
});

/**
 * Clase para manejar la conexion a la base de datos
 */
class Database {
    private static $instance = null;
    private $connection;

    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                die("Error de conexion: " . $e->getMessage());
            } else {
                die("Error de conexion a la base de datos");
            }
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->connection;
    }

    // Prevenir clonacion
    private function __clone() {}

    // Prevenir deserializacion
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * Funcion helper para obtener la conexion a la base de datos
 */
function getDB() {
    return Database::getInstance()->getConnection();
}

/**
 * Funcion para responder con JSON
 */
function jsonResponse($data, $status = 200) {
    // Limpiar cualquier output previo
    if (ob_get_level()) {
        ob_clean();
    }

    http_response_code($status);
    header('Content-Type: application/json; charset=utf-8');

    $json = json_encode($data, JSON_UNESCAPED_UNICODE);
    if ($json === false) {
        // Si hay error en la codificación JSON, enviar un error básico
        $json = json_encode([
            'success' => false,
            'message' => 'Error interno del servidor (JSON encoding failed)'
        ]);
    }

    echo $json;
    exit;
}

/**
 * Funcion para manejar errores de base de datos
 */
function handleDBError($e, $message = "Error en la base de datos") {
    if (DEBUG_MODE) {
        jsonResponse([
            'success' => false,
            'message' => $message,
            'error' => $e->getMessage()
        ], 500);
    } else {
        jsonResponse([
            'success' => false,
            'message' => $message
        ], 500);
    }
}
?>