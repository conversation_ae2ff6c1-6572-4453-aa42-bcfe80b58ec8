<?php
/**
 * Archivo simple para probar la conexión y respuesta JSON
 */

// Iniciar output buffering
ob_start();

require_once 'config.php';

try {
    // Probar conexión a la base de datos
    $db = getDB();
    
    // Probar una consulta simple
    $stmt = $db->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    jsonResponse([
        'success' => true,
        'message' => 'Conexión exitosa',
        'data' => [
            'test_query' => $result['test'],
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    jsonResponse([
        'success' => false,
        'message' => 'Error de conexión',
        'error' => $e->getMessage()
    ], 500);
}
?>
