-- =====================================================
-- SCRIPT DE CREACIÓN DE BASE DE DATOS Y TABLAS
-- Sistema de Agendamiento de Turnos Médicos
-- =====================================================

-- Crear la base de datos
CREATE DATABASE IF NOT EXISTS agenda_medica 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Usar la base de datos
USE agenda_medica;

-- =====================================================
-- TABLA: boxes (Consultorios/Boxes)
-- =====================================================
CREATE TABLE IF NOT EXISTS boxes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    descripcion TEXT NULL,
    activo TINYINT(1) DEFAULT 1,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_boxes_activo (activo),
    INDEX idx_boxes_nombre (nombre)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLA: profesionales (Médicos/Profesionales)
-- =====================================================
CREATE TABLE IF NOT EXISTS profesionales (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    apellido VARCHAR(100) NOT NULL,
    telefono VARCHAR(20) NULL,
    email VARCHAR(150) NULL,
    cedula VARCHAR(20) NULL,
    activo TINYINT(1) DEFAULT 1,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Índices
    INDEX idx_profesionales_activo (activo),
    INDEX idx_profesionales_nombre (nombre, apellido),
    INDEX idx_profesionales_cedula (cedula),
    UNIQUE KEY uk_profesionales_email (email),
    UNIQUE KEY uk_profesionales_cedula (cedula)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLA: turnos (Citas/Agendamientos)
-- =====================================================
CREATE TABLE IF NOT EXISTS turnos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fecha DATE NOT NULL,
    turno ENUM('AM', 'PM') NOT NULL,
    box_id INT NOT NULL,
    profesional_id INT NOT NULL,
    paciente_nombre VARCHAR(200) NULL,
    paciente_telefono VARCHAR(20) NULL,
    paciente_cedula VARCHAR(20) NULL,
    observaciones TEXT NULL,
    estado ENUM('programado', 'confirmado', 'cancelado', 'completado') DEFAULT 'programado',
    activo TINYINT(1) DEFAULT 1,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Claves foráneas
    FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (profesional_id) REFERENCES profesionales(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    
    -- Índices
    INDEX idx_turnos_fecha (fecha),
    INDEX idx_turnos_box_fecha (box_id, fecha),
    INDEX idx_turnos_profesional_fecha (profesional_id, fecha),
    INDEX idx_turnos_activo (activo),
    INDEX idx_turnos_estado (estado),
    
    -- Restricción única: Un box no puede tener dos turnos en la misma fecha y horario
    UNIQUE KEY uk_turnos_box_fecha_turno (box_id, fecha, turno, activo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLA: configuracion (Configuraciones del sistema)
-- =====================================================
CREATE TABLE IF NOT EXISTS configuracion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    clave VARCHAR(100) NOT NULL,
    valor TEXT NOT NULL,
    descripcion TEXT NULL,
    tipo ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    UNIQUE KEY uk_configuracion_clave (clave)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLA: logs (Registro de actividades)
-- =====================================================
CREATE TABLE IF NOT EXISTS logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    accion VARCHAR(100) NOT NULL,
    tabla_afectada VARCHAR(50) NULL,
    registro_id INT NULL,
    datos_anteriores JSON NULL,
    datos_nuevos JSON NULL,
    usuario VARCHAR(100) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_logs_accion (accion),
    INDEX idx_logs_tabla (tabla_afectada),
    INDEX idx_logs_fecha (fecha_creacion),
    INDEX idx_logs_usuario (usuario)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- DATOS INICIALES
-- =====================================================

-- Insertar boxes de ejemplo
INSERT INTO boxes (nombre, descripcion) VALUES
('Box 1', 'Consultorio principal - Planta baja'),
('Box 2', 'Consultorio secundario - Planta baja'),
('Box 3', 'Consultorio especialidades - Primer piso'),
('Box 4', 'Consultorio urgencias - Planta baja');

-- Insertar profesionales de ejemplo
INSERT INTO profesionales (nombre, apellido, telefono, email) VALUES
('Dr. Juan', 'Pérez', '555-0001', '<EMAIL>'),
('Dra. María', 'García', '555-0002', '<EMAIL>'),
('Dr. Carlos', 'López', '555-0003', '<EMAIL>'),
('Dra. Ana', 'Martínez', '555-0004', '<EMAIL>');

-- Insertar configuraciones iniciales
INSERT INTO configuracion (clave, valor, descripcion, tipo) VALUES
('horario_am_inicio', '08:00', 'Hora de inicio del turno AM', 'string'),
('horario_am_fin', '12:00', 'Hora de fin del turno AM', 'string'),
('horario_pm_inicio', '14:00', 'Hora de inicio del turno PM', 'string'),
('horario_pm_fin', '18:00', 'Hora de fin del turno PM', 'string'),
('dias_laborables', '["lunes","martes","miercoles","jueves","viernes"]', 'Días laborables de la semana', 'json'),
('tiempo_cita_minutos', '30', 'Duración estándar de una cita en minutos', 'number'),
('permitir_citas_mismo_dia', 'false', 'Permitir agendar citas para el mismo día', 'boolean'),
('dias_anticipacion_maxima', '30', 'Máximo de días de anticipación para agendar', 'number');

-- =====================================================
-- VISTAS ÚTILES
-- =====================================================

-- Vista para turnos con información completa
CREATE OR REPLACE VIEW vista_turnos_completa AS
SELECT 
    t.id,
    t.fecha,
    t.turno,
    t.paciente_nombre,
    t.paciente_telefono,
    t.paciente_cedula,
    t.observaciones,
    t.estado,
    b.nombre AS box_nombre,
    b.descripcion AS box_descripcion,
    CONCAT(p.nombre, ' ', p.apellido) AS profesional_nombre_completo,
    p.nombre AS profesional_nombre,
    p.apellido AS profesional_apellido,
    p.especialidad AS profesional_especialidad,
    p.telefono AS profesional_telefono,
    p.email AS profesional_email,
    t.fecha_creacion,
    t.fecha_actualizacion
FROM turnos t
JOIN boxes b ON t.box_id = b.id
JOIN profesionales p ON t.profesional_id = p.id
WHERE t.activo = 1 AND b.activo = 1 AND p.activo = 1;

-- Vista para disponibilidad de boxes por fecha
CREATE OR REPLACE VIEW vista_disponibilidad_boxes AS
SELECT 
    b.id AS box_id,
    b.nombre AS box_nombre,
    DATE(CURDATE()) AS fecha,
    'AM' AS turno,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM turnos t 
            WHERE t.box_id = b.id 
            AND t.fecha = CURDATE() 
            AND t.turno = 'AM' 
            AND t.activo = 1
        ) THEN 'ocupado' 
        ELSE 'disponible' 
    END AS estado
FROM boxes b
WHERE b.activo = 1

UNION ALL

SELECT 
    b.id AS box_id,
    b.nombre AS box_nombre,
    DATE(CURDATE()) AS fecha,
    'PM' AS turno,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM turnos t 
            WHERE t.box_id = b.id 
            AND t.fecha = CURDATE() 
            AND t.turno = 'PM' 
            AND t.activo = 1
        ) THEN 'ocupado' 
        ELSE 'disponible' 
    END AS estado
FROM boxes b
WHERE b.activo = 1;

-- =====================================================
-- PROCEDIMIENTOS ALMACENADOS
-- =====================================================

DELIMITER //

-- Procedimiento para obtener disponibilidad de una fecha específica
CREATE PROCEDURE sp_obtener_disponibilidad_fecha(IN fecha_consulta DATE)
BEGIN
    SELECT 
        b.id AS box_id,
        b.nombre AS box_nombre,
        fecha_consulta AS fecha,
        'AM' AS turno,
        CASE 
            WHEN EXISTS (
                SELECT 1 FROM turnos t 
                WHERE t.box_id = b.id 
                AND t.fecha = fecha_consulta 
                AND t.turno = 'AM' 
                AND t.activo = 1
            ) THEN 'ocupado' 
            ELSE 'disponible' 
        END AS estado,
        (SELECT CONCAT(p.nombre, ' ', p.apellido) 
         FROM turnos t 
         JOIN profesionales p ON t.profesional_id = p.id 
         WHERE t.box_id = b.id 
         AND t.fecha = fecha_consulta 
         AND t.turno = 'AM' 
         AND t.activo = 1 
         LIMIT 1) AS profesional_asignado
    FROM boxes b
    WHERE b.activo = 1

    UNION ALL

    SELECT 
        b.id AS box_id,
        b.nombre AS box_nombre,
        fecha_consulta AS fecha,
        'PM' AS turno,
        CASE 
            WHEN EXISTS (
                SELECT 1 FROM turnos t 
                WHERE t.box_id = b.id 
                AND t.fecha = fecha_consulta 
                AND t.turno = 'PM' 
                AND t.activo = 1
            ) THEN 'ocupado' 
            ELSE 'disponible' 
        END AS estado,
        (SELECT CONCAT(p.nombre, ' ', p.apellido) 
         FROM turnos t 
         JOIN profesionales p ON t.profesional_id = p.id 
         WHERE t.box_id = b.id 
         AND t.fecha = fecha_consulta 
         AND t.turno = 'PM' 
         AND t.activo = 1 
         LIMIT 1) AS profesional_asignado
    FROM boxes b
    WHERE b.activo = 1
    ORDER BY box_id, turno;
END //

DELIMITER ;

-- =====================================================
-- MENSAJE DE FINALIZACIÓN
-- =====================================================
SELECT 'Base de datos y tablas creadas exitosamente' AS mensaje;
