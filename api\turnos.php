<?php
/**
 * API para manejo de turnos/citas
 */

// Iniciar output buffering para evitar output no deseado
ob_start();

require_once '../config.php';

// Configurar headers para CORS y JSON
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$db = getDB();

try {
    switch ($method) {
        case 'GET':
            // Obtener turnos por fecha o rango de fechas
            $fecha = isset($_GET['fecha']) ? $_GET['fecha'] : null;
            $fecha_inicio = isset($_GET['fecha_inicio']) ? $_GET['fecha_inicio'] : null;
            $fecha_fin = isset($_GET['fecha_fin']) ? $_GET['fecha_fin'] : null;

            if ($fecha) {
                // Obtener turnos para una fecha específica
                $stmt = $db->prepare("
                    SELECT t.id, t.fecha, t.turno, t.box_id, t.profesional_id, t.observaciones,
                           b.nombre as box_nombre,
                           p.nombre as profesional_nombre, p.apellido as profesional_apellido
                    FROM turnos t
                    JOIN boxes b ON t.box_id = b.id
                    JOIN profesionales p ON t.profesional_id = p.id
                    WHERE t.fecha = ? AND t.activo = 1
                    ORDER BY b.nombre, t.turno
                ");
                $stmt->execute([$fecha]);
            } elseif ($fecha_inicio && $fecha_fin) {
                // Obtener turnos para un rango de fechas
                $stmt = $db->prepare("
                    SELECT t.id, t.fecha, t.turno, t.box_id, t.profesional_id, t.observaciones,
                           b.nombre as box_nombre,
                           p.nombre as profesional_nombre, p.apellido as profesional_apellido
                    FROM turnos t
                    JOIN boxes b ON t.box_id = b.id
                    JOIN profesionales p ON t.profesional_id = p.id
                    WHERE t.fecha BETWEEN ? AND ? AND t.activo = 1
                    ORDER BY t.fecha, b.nombre, t.turno
                ");
                $stmt->execute([$fecha_inicio, $fecha_fin]);
            } else {
                // Obtener todos los turnos activos
                $stmt = $db->prepare("
                    SELECT t.id, t.fecha, t.turno, t.box_id, t.profesional_id, t.observaciones,
                           b.nombre as box_nombre,
                           p.nombre as profesional_nombre, p.apellido as profesional_apellido
                    FROM turnos t
                    JOIN boxes b ON t.box_id = b.id
                    JOIN profesionales p ON t.profesional_id = p.id
                    WHERE t.activo = 1
                    ORDER BY t.fecha DESC, b.nombre, t.turno
                    LIMIT 100
                ");
                $stmt->execute();
            }

            $turnos = $stmt->fetchAll();

            jsonResponse([
                'success' => true,
                'data' => $turnos
            ]);
            break;

        case 'POST':
            // Crear un nuevo turno
            $input = json_decode(file_get_contents('php://input'), true);

            // Validar campos requeridos
            $required_fields = ['fecha', 'turno', 'box_id', 'profesional_id'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    jsonResponse([
                        'success' => false,
                        'message' => "El campo {$field} es requerido"
                    ], 400);
                }
            }

            $fecha = $input['fecha'];
            $turno = $input['turno'];
            $box_id = (int)$input['box_id'];
            $profesional_id = (int)$input['profesional_id'];
            $observaciones = isset($input['observaciones']) ? trim($input['observaciones']) : '';

            // Validar formato de fecha
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha)) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Formato de fecha inválido. Use YYYY-MM-DD'
                ], 400);
            }

            // Validar turno
            if (!in_array($turno, ['AM', 'PM'])) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Turno debe ser AM o PM'
                ], 400);
            }

            // Verificar que el box existe
            $stmt = $db->prepare("SELECT id FROM boxes WHERE id = ? AND activo = 1");
            $stmt->execute([$box_id]);
            if (!$stmt->fetch()) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Box no encontrado'
                ], 404);
            }

            // Verificar que el profesional existe
            $stmt = $db->prepare("SELECT id FROM profesionales WHERE id = ? AND activo = 1");
            $stmt->execute([$profesional_id]);
            if (!$stmt->fetch()) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Profesional no encontrado'
                ], 404);
            }

            // Verificar si ya existe un turno para ese box, fecha y turno
            $stmt = $db->prepare("SELECT id FROM turnos WHERE fecha = ? AND box_id = ? AND turno = ? AND activo = 1");
            $stmt->execute([$fecha, $box_id, $turno]);
            if ($stmt->fetch()) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Ya existe un turno para ese box, fecha y horario'
                ], 400);
            }

            // Verificar si el profesional ya tiene un turno en esa fecha y horario
            $stmt = $db->prepare("SELECT b.nombre as box_nombre FROM turnos t JOIN boxes b ON t.box_id = b.id WHERE t.fecha = ? AND t.profesional_id = ? AND t.turno = ? AND t.activo = 1");
            $stmt->execute([$fecha, $profesional_id, $turno]);
            $conflicto = $stmt->fetch();
            if ($conflicto) {
                jsonResponse([
                    'success' => false,
                    'message' => 'El profesional ya tiene un turno asignado en ese horario',
                    'conflicto' => [
                        'box_nombre' => $conflicto['box_nombre'],
                        'fecha' => $fecha,
                        'turno' => $turno
                    ]
                ], 400);
            }

            // Insertar el nuevo turno
            $stmt = $db->prepare("INSERT INTO turnos (fecha, turno, box_id, profesional_id, observaciones) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$fecha, $turno, $box_id, $profesional_id, $observaciones]);

            $newId = $db->lastInsertId();

            // Obtener los datos completos del turno creado
            $stmt = $db->prepare("
                SELECT t.id, t.fecha, t.turno, t.box_id, t.profesional_id, t.observaciones,
                       b.nombre as box_nombre,
                       p.nombre as profesional_nombre, p.apellido as profesional_apellido
                FROM turnos t
                JOIN boxes b ON t.box_id = b.id
                JOIN profesionales p ON t.profesional_id = p.id
                WHERE t.id = ?
            ");
            $stmt->execute([$newId]);
            $turno_data = $stmt->fetch();

            jsonResponse([
                'success' => true,
                'message' => 'Turno creado correctamente',
                'data' => $turno_data
            ]);
            break;

        case 'DELETE':
            // Eliminar un turno (soft delete)
            $input = json_decode(file_get_contents('php://input'), true);

            if (!isset($input['id'])) {
                jsonResponse([
                    'success' => false,
                    'message' => 'ID es requerido'
                ], 400);
            }

            $id = (int)$input['id'];

            // Verificar si el turno existe
            $stmt = $db->prepare("SELECT id FROM turnos WHERE id = ? AND activo = 1");
            $stmt->execute([$id]);

            if (!$stmt->fetch()) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Turno no encontrado'
                ], 404);
            }

            // Realizar soft delete
            $stmt = $db->prepare("UPDATE turnos SET activo = 0 WHERE id = ?");
            $stmt->execute([$id]);

            jsonResponse([
                'success' => true,
                'message' => 'Turno eliminado correctamente'
            ]);
            break;

        default:
            jsonResponse([
                'success' => false,
                'message' => 'Método no permitido'
            ], 405);
            break;
    }

} catch (PDOException $e) {
    handleDBError($e, "Error al procesar la solicitud de turnos");
} catch (Exception $e) {
    jsonResponse([
        'success' => false,
        'message' => 'Error interno del servidor'
    ], 500);
}
?>