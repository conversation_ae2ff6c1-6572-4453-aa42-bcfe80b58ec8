<?php
/**
 * Script de pruebas para verificar la conectividad y funcionalidad de las APIs
 * Ejecutar desde línea de comandos: php test_apis.php
 */

require_once 'config.php';

echo "=== PRUEBAS DEL SISTEMA DE AGENDAMIENTO ===\n\n";

// Test 1: Conexión a la base de datos
echo "1. Probando conexión a la base de datos...\n";
try {
    $db = getDB();
    echo "   ✓ Conexión exitosa\n\n";
} catch (Exception $e) {
    echo "   ✗ Error de conexión: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Verificar estructura de tablas
echo "2. Verificando estructura de tablas...\n";
$tables = ['boxes', 'profesionales', 'turnos'];
foreach ($tables as $table) {
    try {
        $stmt = $db->query("DESCRIBE $table");
        echo "   ✓ Tabla '$table' existe\n";
    } catch (Exception $e) {
        echo "   ✗ Error en tabla '$table': " . $e->getMessage() . "\n";
    }
}
echo "\n";

// Test 3: Verificar datos iniciales
echo "3. Verificando datos iniciales...\n";
try {
    $stmt = $db->query("SELECT COUNT(*) as count FROM boxes WHERE activo = 1");
    $result = $stmt->fetch();
    echo "   ✓ Boxes encontrados: " . $result['count'] . "\n";

    $stmt = $db->query("SELECT COUNT(*) as count FROM profesionales WHERE activo = 1");
    $result = $stmt->fetch();
    echo "   ✓ Profesionales encontrados: " . $result['count'] . "\n";

    $stmt = $db->query("SELECT COUNT(*) as count FROM turnos WHERE activo = 1");
    $result = $stmt->fetch();
    echo "   ✓ Turnos encontrados: " . $result['count'] . "\n";
} catch (Exception $e) {
    echo "   ✗ Error verificando datos: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 4: Probar operaciones CRUD en boxes
echo "4. Probando operaciones CRUD en boxes...\n";
try {
    // Crear
    $stmt = $db->prepare("INSERT INTO boxes (nombre) VALUES (?)");
    $stmt->execute(['Box Test']);
    $testBoxId = $db->lastInsertId();
    echo "   ✓ Box creado con ID: $testBoxId\n";

    // Leer
    $stmt = $db->prepare("SELECT * FROM boxes WHERE id = ?");
    $stmt->execute([$testBoxId]);
    $box = $stmt->fetch();
    echo "   ✓ Box leído: " . $box['nombre'] . "\n";

    // Actualizar
    $stmt = $db->prepare("UPDATE boxes SET nombre = ? WHERE id = ?");
    $stmt->execute(['Box Test Actualizado', $testBoxId]);
    echo "   ✓ Box actualizado\n";

    // Eliminar (soft delete)
    $stmt = $db->prepare("UPDATE boxes SET activo = 0 WHERE id = ?");
    $stmt->execute([$testBoxId]);
    echo "   ✓ Box eliminado (soft delete)\n";

} catch (Exception $e) {
    echo "   ✗ Error en CRUD boxes: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 5: Probar operaciones CRUD en profesionales
echo "5. Probando operaciones CRUD en profesionales...\n";
try {
    // Crear
    $stmt = $db->prepare("INSERT INTO profesionales (nombre, apellido) VALUES (?, ?)");
    $stmt->execute(['Test', 'Profesional']);
    $testProfId = $db->lastInsertId();
    echo "   ✓ Profesional creado con ID: $testProfId\n";

    // Leer
    $stmt = $db->prepare("SELECT * FROM profesionales WHERE id = ?");
    $stmt->execute([$testProfId]);
    $prof = $stmt->fetch();
    echo "   ✓ Profesional leído: " . $prof['nombre'] . " " . $prof['apellido'] . "\n";

    // Actualizar
    $stmt = $db->prepare("UPDATE profesionales SET nombre = ?, apellido = ? WHERE id = ?");
    $stmt->execute(['Test Actualizado', 'Profesional Actualizado', $testProfId]);
    echo "   ✓ Profesional actualizado\n";

    // Eliminar (soft delete)
    $stmt = $db->prepare("UPDATE profesionales SET activo = 0 WHERE id = ?");
    $stmt->execute([$testProfId]);
    echo "   ✓ Profesional eliminado (soft delete)\n";

} catch (Exception $e) {
    echo "   ✗ Error en CRUD profesionales: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 6: Probar restricciones de integridad
echo "6. Probando restricciones de integridad...\n";
try {
    // Obtener un box y profesional válidos
    $stmt = $db->query("SELECT id FROM boxes WHERE activo = 1 LIMIT 1");
    $box = $stmt->fetch();

    $stmt = $db->query("SELECT id FROM profesionales WHERE activo = 1 LIMIT 1");
    $prof = $stmt->fetch();

    if ($box && $prof) {
        // Crear turno
        $stmt = $db->prepare("INSERT INTO turnos (fecha, turno, box_id, profesional_id) VALUES (?, ?, ?, ?)");
        $stmt->execute(['2025-12-25', 'AM', $box['id'], $prof['id']]);
        $testTurnoId = $db->lastInsertId();
        echo "   ✓ Turno creado con ID: $testTurnoId\n";

        // Intentar crear turno duplicado (debe fallar)
        try {
            $stmt->execute(['2025-12-25', 'AM', $box['id'], $prof['id']]);
            echo "   ✗ ERROR: Se permitió turno duplicado\n";
        } catch (Exception $e) {
            echo "   ✓ Restricción de turno único funcionando\n";
        }

        // Limpiar
        $stmt = $db->prepare("UPDATE turnos SET activo = 0 WHERE id = ?");
        $stmt->execute([$testTurnoId]);
        echo "   ✓ Turno de prueba eliminado\n";
    } else {
        echo "   ⚠ No hay datos suficientes para probar turnos\n";
    }

} catch (Exception $e) {
    echo "   ✗ Error en pruebas de integridad: " . $e->getMessage() . "\n";
}
echo "\n";

echo "=== PRUEBAS COMPLETADAS ===\n";
echo "El sistema está listo para usar.\n";
echo "Accede a index.php desde tu navegador para usar la interfaz web.\n";
?>