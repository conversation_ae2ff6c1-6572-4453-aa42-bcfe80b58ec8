<?php
/**
 * API para manejo de profesionales
 */

// Iniciar output buffering para evitar output no deseado
ob_start();

require_once '../config.php';

// Configurar headers para CORS y JSON
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$db = getDB();

try {
    switch ($method) {
        case 'GET':
            // Obtener todos los profesionales activos
            $stmt = $db->prepare("SELECT id, nombre, apellido FROM profesionales WHERE activo = 1 ORDER BY apellido, nombre");
            $stmt->execute();
            $profesionales = $stmt->fetchAll();

            jsonResponse([
                'success' => true,
                'data' => $profesionales
            ]);
            break;

        case 'POST':
            // Crear un nuevo profesional
            $input = json_decode(file_get_contents('php://input'), true);

            if (!isset($input['nombre']) || empty(trim($input['nombre']))) {
                jsonResponse([
                    'success' => false,
                    'message' => 'El nombre del profesional es requerido'
                ], 400);
            }

            $nombre = trim($input['nombre']);
            $apellido = isset($input['apellido']) ? trim($input['apellido']) : '';

            // Si solo se proporciona un campo, intentar separar nombre y apellido
            if (empty($apellido) && strpos($nombre, ' ') !== false) {
                $parts = explode(' ', $nombre, 2);
                $nombre = $parts[0];
                $apellido = $parts[1];
            }

            // Insertar el nuevo profesional
            $stmt = $db->prepare("INSERT INTO profesionales (nombre, apellido) VALUES (?, ?)");
            $stmt->execute([$nombre, $apellido]);

            $newId = $db->lastInsertId();

            jsonResponse([
                'success' => true,
                'message' => 'Profesional creado correctamente',
                'data' => [
                    'id' => $newId,
                    'nombre' => $nombre,
                    'apellido' => $apellido
                ]
            ]);
            break;

        case 'PUT':
            // Actualizar un profesional existente
            $input = json_decode(file_get_contents('php://input'), true);

            if (!isset($input['id']) || !isset($input['nombre']) || empty(trim($input['nombre']))) {
                jsonResponse([
                    'success' => false,
                    'message' => 'ID y nombre son requeridos'
                ], 400);
            }

            $id = (int)$input['id'];
            $nombre = trim($input['nombre']);
            $apellido = isset($input['apellido']) ? trim($input['apellido']) : '';

            // Si solo se proporciona un campo, intentar separar nombre y apellido
            if (empty($apellido) && strpos($nombre, ' ') !== false) {
                $parts = explode(' ', $nombre, 2);
                $nombre = $parts[0];
                $apellido = $parts[1];
            }

            // Verificar si el profesional existe
            $stmt = $db->prepare("SELECT id FROM profesionales WHERE id = ? AND activo = 1");
            $stmt->execute([$id]);

            if (!$stmt->fetch()) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Profesional no encontrado'
                ], 404);
            }

            // Actualizar el profesional
            $stmt = $db->prepare("UPDATE profesionales SET nombre = ?, apellido = ? WHERE id = ?");
            $stmt->execute([$nombre, $apellido, $id]);

            jsonResponse([
                'success' => true,
                'message' => 'Profesional actualizado correctamente',
                'data' => [
                    'id' => $id,
                    'nombre' => $nombre,
                    'apellido' => $apellido
                ]
            ]);
            break;

        case 'DELETE':
            // Eliminar un profesional (soft delete)
            $input = json_decode(file_get_contents('php://input'), true);

            if (!isset($input['id'])) {
                jsonResponse([
                    'success' => false,
                    'message' => 'ID es requerido'
                ], 400);
            }

            $id = (int)$input['id'];

            // Verificar si el profesional existe
            $stmt = $db->prepare("SELECT id FROM profesionales WHERE id = ? AND activo = 1");
            $stmt->execute([$id]);

            if (!$stmt->fetch()) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Profesional no encontrado'
                ], 404);
            }

            // Verificar si hay turnos asociados al profesional
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM turnos WHERE profesional_id = ? AND activo = 1");
            $stmt->execute([$id]);
            $result = $stmt->fetch();

            if ($result['count'] > 0) {
                jsonResponse([
                    'success' => false,
                    'message' => 'No se puede eliminar el profesional porque tiene turnos asociados'
                ], 400);
            }

            // Realizar soft delete
            $stmt = $db->prepare("UPDATE profesionales SET activo = 0 WHERE id = ?");
            $stmt->execute([$id]);

            jsonResponse([
                'success' => true,
                'message' => 'Profesional eliminado correctamente'
            ]);
            break;

        default:
            jsonResponse([
                'success' => false,
                'message' => 'Método no permitido'
            ], 405);
            break;
    }

} catch (PDOException $e) {
    handleDBError($e, "Error al procesar la solicitud de profesionales");
} catch (Exception $e) {
    jsonResponse([
        'success' => false,
        'message' => 'Error interno del servidor'
    ], 500);
}
?>