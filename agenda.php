<?php
// Archivo PHP para el sistema de agendamiento de turnos
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Agendamiento de Turnos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Panel lateral izquierdo */
        .left-panel {
            width: 250px;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            overflow-y: auto;
        }

        .left-panel h2 {
            margin-bottom: 20px;
            text-align: center;
            color: #ecf0f1;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h3 {
            margin-bottom: 15px;
            color: #3498db;
            border-bottom: 1px solid #3498db;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 4px;
        }

        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #2980b9;
        }

        .btn-danger {
            background-color: #e74c3c;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .list-item {
            background-color: #34495e;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .list-item span {
            flex-grow: 1;
        }

        .list-item .btn {
            padding: 2px 6px;
            font-size: 10px;
            margin-left: 3px;
            min-width: auto;
        }

        /* Contenido principal */
        .main-content {
            flex-grow: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .header {
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        /* Calendario */
        .calendar-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .calendar-header h2 {
            color: #2c3e50;
        }

        .calendar-nav {
            display: flex;
        }

        .calendar-nav button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            margin-left: 5px;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 5px;
        }

        .calendar-day-header {
            text-align: center;
            font-weight: bold;
            padding: 10px 0;
            color: #7f8c8d;
        }

        .calendar-day {
            min-height: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            padding: 5px;
            position: relative;
            border: 1px solid #eee;
        }

        .calendar-day:hover {
            background-color: #e3f2fd;
        }

        .calendar-day.other-month {
            color: #bdc3c7;
            background-color: #f8f9fa;
        }

        .calendar-day.selected {
            background-color: #3498db;
            color: white;
        }

        .calendar-day.today {
            background-color: #e3f2fd;
            font-weight: bold;
        }

        .day-number {
            font-weight: bold;
            margin-bottom: 5px;
            align-self: flex-start;
        }

        .appointment-list {
            width: 100%;
            font-size: 11px;
            overflow-y: auto;
            flex-grow: 1;
        }

        .appointment-item {
            margin-bottom: 3px;
            padding: 2px 4px;
            border-radius: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .legend {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 10px 5px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }

        /* Panel lateral derecho */
        .right-panel {
            width: 300px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-left: 20px;
        }

        .right-panel h2 {
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }

        .section-frame {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }

        .section-frame h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }

        .box-availability {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 4px;
            background-color: white;
            border: 1px solid #eee;
        }

        .box-availability h4 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .turn-availability {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .turn-availability span {
            font-weight: bold;
        }

        .available {
            color: #27ae60;
        }

        .unavailable {
            color: #e74c3c;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background-color: #2ecc71;
            color: white;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transform: translateX(200%);
            transition: transform 0.3s ease-out;
            z-index: 1000;
            max-width: 400px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background-color: #e74c3c;
        }

        /* Modal para conflicto de agendamiento */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .modal-header h3 {
            color: #e74c3c;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #7f8c8d;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
        }

        .modal-footer .btn {
            margin-left: 10px;
            padding: 8px 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Panel lateral izquierdo -->
        <div class="left-panel">
            <h2>Administración</h2>
            
            <!-- Sección de Boxes -->
            <div class="section">
                <h3>Gestión de Boxes</h3>
                <div class="form-group">
                    <label for="boxName">Nombre del Box</label>
                    <input type="text" id="boxName" placeholder="Ej: Box 1">
                </div>
                <button class="btn" id="addBox">Agregar Box</button>
                
                <div id="boxesList" style="margin-top: 15px;">
                    <!-- Lista de boxes se generará dinámicamente -->
                </div>
            </div>
            
            <!-- Sección de Profesionales -->
            <div class="section">
                <h3>Gestión de Profesionales</h3>
                <div class="form-group">
                    <label for="profName">Nombre del Profesional</label>
                    <input type="text" id="profName" placeholder="Ej: Pérez">
                </div>
                <button class="btn" id="addProf">Agregar Profesional</button>
                
                <div id="profsList" style="margin-top: 15px;">
                    <!-- Lista de profesionales se generará dinámicamente -->
                </div>
            </div>
        </div>
        
        <!-- Contenido principal -->
        <div class="main-content">
            <div class="header">
                <h1>Sistema de Agendamiento de Turnos</h1>
            </div>
            
            <!-- Calendario -->
            <div class="calendar-container">
                <div class="calendar-header">
                    <h2 id="currentMonth">Mes Actual</h2>
                    <div class="calendar-nav">
                        <button id="prevMonth">Anterior</button>
                        <button id="nextMonth">Siguiente</button>
                    </div>
                </div>
                <div class="calendar-grid" id="calendarGrid">
                    <!-- Calendario se generará dinámicamente -->
                </div>
                <div class="legend" id="calendarLegend">
                    <!-- Leyenda se generará dinámicamente -->
                </div>
            </div>
        </div>
        
        <!-- Panel lateral derecho -->
        <div class="right-panel">
            <h2>Disponibilidad de Turnos</h2>
            
            <div class="section-frame">
                <h3 id="selectedDate">Seleccione una fecha</h3>
                <div id="boxesAvailability">
                    <!-- Disponibilidad de boxes se generará dinámicamente -->
                </div>
            </div>
            
            <div class="section-frame">
                <h3>Agendar Turno</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="bookingBox">Box</label>
                        <select id="bookingBox">
                            <!-- Opciones se generarán dinámicamente -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bookingTurn">Turno</label>
                        <select id="bookingTurn">
                            <option value="AM">AM</option>
                            <option value="PM">PM</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="bookingProf">Profesional</label>
                    <select id="bookingProf">
                        <!-- Opciones se generarán dinámicamente -->
                    </select>
                </div>
                <button class="btn" id="bookAppointment">Agendar Turno</button>
            </div>
        </div>
    </div>
    
    <!-- Notificación -->
    <div class="notification" id="notification"></div>
    
    <!-- Modal para conflicto de agendamiento -->
    <div class="modal" id="conflictModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Conflicto de Agendamiento</h3>
                <button class="modal-close" id="closeModal">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Contenido del modal se generará dinámicamente -->
            </div>
            <div class="modal-footer">
                <button class="btn" id="cancelBooking">Cancelar</button>
            </div>
        </div>
    </div>
    
    <script>
        // Datos iniciales
        let boxes = [
            { id: 1, name: 'Box 1' },
            { id: 2, name: 'Box 2' }
        ];
        
        let professionals = [
            { id: 1, name: 'Juan', lastName: 'Pérez' },
            { id: 2, name: 'María', lastName: 'García' }
        ];
        
        // Estructura para almacenar los turnos agendados
        let appointments = {};
        
        // Fecha actual y seleccionada
        let currentDate = new Date();
        let selectedDate = null;
        
        // Colores para los boxes
        const boxColors = [
            '#3498db', // Azul
            '#e74c3c', // Rojo
            '#2ecc71', // Verde
            '#f39c12', // Naranja
            '#9b59b6', // Púrpura
            '#1abc9c', // Turquesa
            '#d35400', // Naranja oscuro
            '#c0392b', // Rojo oscuro
            '#8e44ad', // Púrpura oscuro
            '#27ae60'  // Verde oscuro
        ];
        
        // Elementos del DOM
        const calendarGrid = document.getElementById('calendarGrid');
        const currentMonthElement = document.getElementById('currentMonth');
        const prevMonthButton = document.getElementById('prevMonth');
        const nextMonthButton = document.getElementById('nextMonth');
        const boxesListElement = document.getElementById('boxesList');
        const profsListElement = document.getElementById('profsList');
        const selectedDateElement = document.getElementById('selectedDate');
        const boxesAvailabilityElement = document.getElementById('boxesAvailability');
        const bookingBoxElement = document.getElementById('bookingBox');
        const bookingProfElement = document.getElementById('bookingProf');
        const notificationElement = document.getElementById('notification');
        const calendarLegendElement = document.getElementById('calendarLegend');
        const conflictModal = document.getElementById('conflictModal');
        const modalBody = document.getElementById('modalBody');
        const closeModalButton = document.getElementById('closeModal');
        const cancelButton = document.getElementById('cancelBooking');
        
        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            renderCalendar();
            renderBoxesList();
            renderProfessionalsList();
            updateBookingOptions();
            renderLegend();
            
            // Event listeners
            prevMonthButton.addEventListener('click', () => {
                currentDate.setMonth(currentDate.getMonth() - 1);
                renderCalendar();
            });
            
            nextMonthButton.addEventListener('click', () => {
                currentDate.setMonth(currentDate.getMonth() + 1);
                renderCalendar();
            });
            
            document.getElementById('addBox').addEventListener('click', addBox);
            document.getElementById('addProf').addEventListener('click', addProfessional);
            document.getElementById('bookAppointment').addEventListener('click', bookAppointment);
            closeModalButton.addEventListener('click', closeModal);
            cancelButton.addEventListener('click', closeModal);
            
            // Cerrar modal al hacer clic fuera del contenido
            conflictModal.addEventListener('click', function(e) {
                if (e.target === conflictModal) {
                    closeModal();
                }
            });
        });
        
        // Renderizar el calendario
        function renderCalendar() {
            // Actualizar el encabezado del mes
            const monthNames = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 
                               'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];
            currentMonthElement.textContent = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
            
            // Limpiar el calendario
            calendarGrid.innerHTML = '';
            
            // Agregar encabezados de días de la semana (solo de lunes a viernes)
            const dayHeaders = ['Lun', 'Mar', 'Mié', 'Jue', 'Vie'];
            dayHeaders.forEach(day => {
                const dayElement = document.createElement('div');
                dayElement.classList.add('calendar-day-header');
                dayElement.textContent = day;
                calendarGrid.appendChild(dayElement);
            });
            
            // Obtener el primer día del mes y el último día del mes
            const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
            const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
            
            // Obtener el día de la semana del primer día (0 = Domingo, 6 = Sábado)
            let firstDayOfWeek = firstDay.getDay();
            
            // Ajustar para que la semana comience en lunes (1) en lugar de domingo (0)
            if (firstDayOfWeek === 0) {
                firstDayOfWeek = 7; // Si es domingo, lo tratamos como el día 7
            }
            
            // Calcular cuántos días del mes anterior necesitamos mostrar
            const daysFromPrevMonth = firstDayOfWeek - 1; // Restamos 1 porque empezamos en lunes
            
            // Agregar días del mes anterior (solo días de semana)
            const prevMonthLastDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0).getDate();
            for (let i = daysFromPrevMonth; i > 0; i--) {
                const dayNumber = prevMonthLastDay - i + 1;
                const dayDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, dayNumber);
                
                // Solo agregar si es un día de semana (lunes a viernes)
                if (dayDate.getDay() !== 0 && dayDate.getDay() !== 6) {
                    const dayElement = createCalendarDay(dayDate, dayNumber, true);
                    calendarGrid.appendChild(dayElement);
                }
            }
            
            // Agregar días del mes actual (solo días de semana)
            const today = new Date();
            for (let i = 1; i <= lastDay.getDate(); i++) {
                const dayDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), i);
                
                // Solo agregar si es un día de semana (lunes a viernes)
                if (dayDate.getDay() !== 0 && dayDate.getDay() !== 6) {
                    const dayElement = createCalendarDay(dayDate, i, false);
                    
                    // Marcar el día actual
                    if (currentDate.getFullYear() === today.getFullYear() && 
                        currentDate.getMonth() === today.getMonth() && 
                        i === today.getDate()) {
                        dayElement.classList.add('today');
                    }
                    
                    calendarGrid.appendChild(dayElement);
                }
            }
            
            // Agregar días del mes siguiente para completar la cuadrícula (solo días de semana)
            const totalCells = calendarGrid.children.length;
            const remainingCells = 35 - totalCells; // 5 filas x 5 días = 25 celdas (mínimo) o 35 (máximo)
            let dayCounter = 1;
            
            while (calendarGrid.children.length < 35) {
                const dayDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, dayCounter);
                
                // Solo agregar si es un día de semana (lunes a viernes)
                if (dayDate.getDay() !== 0 && dayDate.getDay() !== 6) {
                    const dayElement = createCalendarDay(dayDate, dayCounter, true);
                    calendarGrid.appendChild(dayElement);
                }
                
                dayCounter++;
            }
        }
        
        // Crear un elemento de día del calendario
        function createCalendarDay(date, dayNumber, isOtherMonth) {
            const dayElement = document.createElement('div');
            dayElement.classList.add('calendar-day');
            if (isOtherMonth) {
                dayElement.classList.add('other-month');
            }
            
            // Número del día
            const dayNumberElement = document.createElement('div');
            dayNumberElement.classList.add('day-number');
            dayNumberElement.textContent = dayNumber;
            dayElement.appendChild(dayNumberElement);
            
            // Contenedor para la lista de turnos
            const appointmentsList = document.createElement('div');
            appointmentsList.classList.add('appointment-list');
            
            // Obtener los turnos para este día
            const dateKey = formatDateKey(date);
            
            // Verificar cada box y turno para este día
            boxes.forEach(box => {
                ['AM', 'PM'].forEach(turn => {
                    const appointmentKey = `${dateKey}-${box.id}-${turn}`;
                    if (appointments[appointmentKey]) {
                        // Obtener el profesional asignado
                        const prof = professionals.find(p => p.id === appointments[appointmentKey].profId);
                        
                        if (prof) {
                            // Crear elemento para mostrar el turno
                            const appointmentItem = document.createElement('div');
                            appointmentItem.classList.add('appointment-item');
                            appointmentItem.style.backgroundColor = boxColors[(box.id - 1) % boxColors.length];
                            appointmentItem.style.color = 'white';
                            
                            // Formato: Box, Apellido + inicial del nombre, Turno
                            const profInitial = prof.name.charAt(0);
                            appointmentItem.textContent = `${box.name} - ${prof.lastName} ${profInitial}. - ${turn}`;
                            
                            appointmentsList.appendChild(appointmentItem);
                        }
                    }
                });
            });
            
            dayElement.appendChild(appointmentsList);
            
            // Agregar evento de clic
            dayElement.addEventListener('click', function() {
                // Eliminar la clase selected de todos los días
                document.querySelectorAll('.calendar-day').forEach(day => {
                    day.classList.remove('selected');
                });
                
                // Agregar la clase selected al día seleccionado
                this.classList.add('selected');
                
                // Actualizar la fecha seleccionada
                selectedDate = new Date(date);
                
                // Actualizar la disponibilidad
                updateAvailability();
            });
            
            return dayElement;
        }
        
        // Renderizar la leyenda del calendario
        function renderLegend() {
            calendarLegendElement.innerHTML = '';
            
            boxes.forEach((box, index) => {
                const legendItem = document.createElement('div');
                legendItem.classList.add('legend-item');
                
                const legendColor = document.createElement('div');
                legendColor.classList.add('legend-color');
                legendColor.style.backgroundColor = boxColors[index % boxColors.length];
                
                const legendText = document.createElement('span');
                legendText.textContent = box.name;
                
                legendItem.appendChild(legendColor);
                legendItem.appendChild(legendText);
                calendarLegendElement.appendChild(legendItem);
            });
        }
        
        // Renderizar la lista de boxes
        function renderBoxesList() {
            boxesListElement.innerHTML = '';
            
            boxes.forEach(box => {
                const boxItem = document.createElement('div');
                boxItem.classList.add('list-item');
                
                const boxName = document.createElement('span');
                boxName.textContent = box.name;
                
                const editButton = document.createElement('button');
                editButton.classList.add('btn');
                editButton.textContent = 'Edit';
                editButton.addEventListener('click', () => editBox(box.id));
                
                const deleteButton = document.createElement('button');
                deleteButton.classList.add('btn', 'btn-danger');
                deleteButton.textContent = 'Elim';
                deleteButton.addEventListener('click', () => deleteBox(box.id));
                
                boxItem.appendChild(boxName);
                boxItem.appendChild(editButton);
                boxItem.appendChild(deleteButton);
                
                boxesListElement.appendChild(boxItem);
            });
            
            updateBookingOptions();
            renderLegend();
        }
        
        // Renderizar la lista de profesionales
        function renderProfessionalsList() {
            profsListElement.innerHTML = '';
            
            professionals.forEach(prof => {
                const profItem = document.createElement('div');
                profItem.classList.add('list-item');
                
                const profInfo = document.createElement('span');
                profInfo.textContent = `${prof.name} ${prof.lastName}`;
                
                const editButton = document.createElement('button');
                editButton.classList.add('btn');
                editButton.textContent = 'Edit';
                editButton.addEventListener('click', () => editProfessional(prof.id));
                
                const deleteButton = document.createElement('button');
                deleteButton.classList.add('btn', 'btn-danger');
                deleteButton.textContent = 'Elim';
                deleteButton.addEventListener('click', () => deleteProfessional(prof.id));
                
                profItem.appendChild(profInfo);
                profItem.appendChild(editButton);
                profItem.appendChild(deleteButton);
                
                profsListElement.appendChild(profItem);
            });
            
            updateBookingOptions();
        }
        
        // Agregar un nuevo box
        function addBox() {
            const boxNameInput = document.getElementById('boxName');
            const boxName = boxNameInput.value.trim();
            
            if (boxName === '') {
                showNotification('Por favor, ingrese un nombre para el box', 'error');
                return;
            }
            
            const newBox = {
                id: boxes.length > 0 ? Math.max(...boxes.map(b => b.id)) + 1 : 1,
                name: boxName
            };
            
            boxes.push(newBox);
            renderBoxesList();
            boxNameInput.value = '';
            showNotification('Box agregado correctamente');
        }
        
        // Editar un box
        function editBox(id) {
            const box = boxes.find(b => b.id === id);
            if (!box) return;
            
            const newName = prompt('Editar nombre del box:', box.name);
            if (newName !== null && newName.trim() !== '') {
                box.name = newName.trim();
                renderBoxesList();
                showNotification('Box actualizado correctamente');
            }
        }
        
        // Eliminar un box
        function deleteBox(id) {
            if (confirm('¿Está seguro de que desea eliminar este box?')) {
                boxes = boxes.filter(b => b.id !== id);
                renderBoxesList();
                showNotification('Box eliminado correctamente');
            }
        }
        
        // Agregar un nuevo profesional
        function addProfessional() {
            const profNameInput = document.getElementById('profName');
            const profName = profNameInput.value.trim();
            
            if (profName === '') {
                showNotification('Por favor, ingrese el nombre del profesional', 'error');
                return;
            }
            
            // Separar nombre y apellido
            const nameParts = profName.split(' ');
            let name = '';
            let lastName = '';
            
            if (nameParts.length === 1) {
                name = nameParts[0];
                lastName = '';
            } else {
                name = nameParts[0];
                lastName = nameParts.slice(1).join(' ');
            }
            
            const newProf = {
                id: professionals.length > 0 ? Math.max(...professionals.map(p => p.id)) + 1 : 1,
                name: name,
                lastName: lastName
            };
            
            professionals.push(newProf);
            renderProfessionalsList();
            profNameInput.value = '';
            showNotification('Profesional agregado correctamente');
        }
        
        // Editar un profesional
        function editProfessional(id) {
            const prof = professionals.find(p => p.id === id);
            if (!prof) return;
            
            const newName = prompt('Editar nombre del profesional:', `${prof.name} ${prof.lastName}`);
            if (newName === null) return;
            
            // Separar nombre y apellido
            const nameParts = newName.trim().split(' ');
            let name = '';
            let lastName = '';
            
            if (nameParts.length === 1) {
                name = nameParts[0];
                lastName = '';
            } else {
                name = nameParts[0];
                lastName = nameParts.slice(1).join(' ');
            }
            
            if (name.trim() !== '') {
                prof.name = name.trim();
                prof.lastName = lastName.trim();
                renderProfessionalsList();
                showNotification('Profesional actualizado correctamente');
            }
        }
        
        // Eliminar un profesional
        function deleteProfessional(id) {
            if (confirm('¿Está seguro de que desea eliminar este profesional?')) {
                professionals = professionals.filter(p => p.id !== id);
                renderProfessionalsList();
                showNotification('Profesional eliminado correctamente');
            }
        }
        
        // Actualizar las opciones de box y profesional en el formulario de agendamiento
        function updateBookingOptions() {
            // Actualizar opciones de boxes
            bookingBoxElement.innerHTML = '';
            boxes.forEach(box => {
                const option = document.createElement('option');
                option.value = box.id;
                option.textContent = box.name;
                bookingBoxElement.appendChild(option);
            });
            
            // Actualizar opciones de profesionales
            bookingProfElement.innerHTML = '';
            professionals.forEach(prof => {
                const option = document.createElement('option');
                option.value = prof.id;
                option.textContent = `${prof.name} ${prof.lastName}`;
                bookingProfElement.appendChild(option);
            });
        }
        
        // Actualizar la disponibilidad de boxes para la fecha seleccionada
        function updateAvailability() {
            if (!selectedDate) {
                selectedDateElement.textContent = 'Seleccione una fecha';
                boxesAvailabilityElement.innerHTML = '';
                return;
            }
            
            // Formatear la fecha seleccionada
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            selectedDateElement.textContent = selectedDate.toLocaleDateString('es-ES', options);
            
            // Crear una clave para la fecha seleccionada
            const dateKey = formatDateKey(selectedDate);
            
            // Limpiar el contenedor de disponibilidad
            boxesAvailabilityElement.innerHTML = '';
            
            // Mostrar la disponibilidad de cada box
            boxes.forEach(box => {
                const boxContainer = document.createElement('div');
                boxContainer.classList.add('box-availability');
                
                const boxTitle = document.createElement('h4');
                boxTitle.textContent = box.name;
                boxContainer.appendChild(boxTitle);
                
                // Verificar disponibilidad AM
                const amAvailability = document.createElement('div');
                amAvailability.classList.add('turn-availability');
                
                const amLabel = document.createElement('span');
                amLabel.textContent = 'Turno AM:';
                
                const amStatus = document.createElement('span');
                const amKey = `${dateKey}-${box.id}-AM`;
                const amAvailable = !appointments[amKey];
                
                if (amAvailable) {
                    amStatus.textContent = 'Disponible';
                    amStatus.classList.add('available');
                } else {
                    const prof = professionals.find(p => p.id === appointments[amKey].profId);
                    amStatus.textContent = prof ? `${prof.lastName} ${prof.name.charAt(0)}.` : 'Profesional no encontrado';
                    amStatus.classList.add('unavailable');
                }
                
                amAvailability.appendChild(amLabel);
                amAvailability.appendChild(amStatus);
                boxContainer.appendChild(amAvailability);
                
                // Verificar disponibilidad PM
                const pmAvailability = document.createElement('div');
                pmAvailability.classList.add('turn-availability');
                
                const pmLabel = document.createElement('span');
                pmLabel.textContent = 'Turno PM:';
                
                const pmStatus = document.createElement('span');
                const pmKey = `${dateKey}-${box.id}-PM`;
                const pmAvailable = !appointments[pmKey];
                
                if (pmAvailable) {
                    pmStatus.textContent = 'Disponible';
                    pmStatus.classList.add('available');
                } else {
                    const prof = professionals.find(p => p.id === appointments[pmKey].profId);
                    pmStatus.textContent = prof ? `${prof.lastName} ${prof.name.charAt(0)}.` : 'Profesional no encontrado';
                    pmStatus.classList.add('unavailable');
                }
                
                pmAvailability.appendChild(pmLabel);
                pmAvailability.appendChild(pmStatus);
                boxContainer.appendChild(pmAvailability);
                
                boxesAvailabilityElement.appendChild(boxContainer);
            });
        }
        
        // Verificar si un profesional ya tiene un turno asignado en una fecha y turno específicos
        function checkProfessionalConflict(date, profId, turn) {
            const dateKey = formatDateKey(date);
            
            // Verificar todos los boxes para la misma fecha y turno
            for (const box of boxes) {
                const appointmentKey = `${dateKey}-${box.id}-${turn}`;
                if (appointments[appointmentKey] && appointments[appointmentKey].profId === profId) {
                    // Hay un conflicto, retornar la información del turno existente
                    return {
                        box: box,
                        exists: true
                    };
                }
            }
            
            // No hay conflicto
            return { exists: false };
        }
        
        // Agendar un turno
        function bookAppointment() {
            if (!selectedDate) {
                showNotification('Por favor, seleccione una fecha', 'error');
                return;
            }
            
            const boxId = parseInt(document.getElementById('bookingBox').value);
            const turn = document.getElementById('bookingTurn').value;
            const profId = parseInt(document.getElementById('bookingProf').value);
            
            // Verificar si el profesional ya tiene un turno asignado en el mismo horario
            const conflict = checkProfessionalConflict(selectedDate, profId, turn);
            
            if (conflict.exists) {
                // Mostrar modal con la información del conflicto
                const prof = professionals.find(p => p.id === profId);
                const profName = prof ? `${prof.lastName} ${prof.name.charAt(0)}.` : 'Profesional no encontrado';
                
                modalBody.innerHTML = `
                    <p>El profesional <strong>${profName}</strong> ya tiene un turno asignado en el horario seleccionado:</p>
                    <p><strong>Box:</strong> ${conflict.box.name}</p>
                    <p><strong>Turno:</strong> ${turn}</p>
                    <p><strong>Fecha:</strong> ${selectedDate.toLocaleDateString('es-ES', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
                    <p>Por favor, seleccione otro profesional, otro turno u otra fecha.</p>
                `;
                
                conflictModal.style.display = 'flex';
                return;
            }
            
            // Crear una clave para la fecha y turno seleccionados
            const dateKey = formatDateKey(selectedDate);
            const appointmentKey = `${dateKey}-${boxId}-${turn}`;
            
            // Verificar si el turno ya está ocupado
            if (appointments[appointmentKey]) {
                showNotification('El turno seleccionado ya está ocupado', 'error');
                return;
            }
            
            // Agendar el turno
            appointments[appointmentKey] = {
                boxId,
                turn,
                profId,
                date: selectedDate
            };
            
            // Actualizar el calendario para mostrar los nuevos turnos
            renderCalendar();
            
            // Actualizar la disponibilidad
            updateAvailability();
            
            showNotification('Turno agendado correctamente');
        }
        
        // Cerrar el modal de conflicto
        function closeModal() {
            conflictModal.style.display = 'none';
        }
        
        // Formatear una fecha como una clave (YYYY-MM-DD)
        function formatDateKey(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // Mostrar una notificación
        function showNotification(message, type = 'success') {
            notificationElement.textContent = message;
            notificationElement.className = 'notification';
            
            if (type === 'error') {
                notificationElement.classList.add('error');
            }
            
            notificationElement.classList.add('show');
            
            setTimeout(() => {
                notificationElement.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>