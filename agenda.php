<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Agendamiento de Turnos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Panel lateral izquierdo */
        .left-panel {
            width: 250px;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            overflow-y: auto;
        }

        .left-panel h2 {
            margin-bottom: 20px;
            text-align: center;
            color: #ecf0f1;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h3 {
            margin-bottom: 15px;
            color: #3498db;
            border-bottom: 1px solid #3498db;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 4px;
        }

        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #2980b9;
        }

        .btn-danger {
            background-color: #e74c3c;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .list-item {
            background-color: #34495e;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .list-item span {
            flex-grow: 1;
        }

        .list-item .btn {
            padding: 2px 6px;
            font-size: 10px;
            margin-left: 3px;
            min-width: auto;
        }

        /* Contenido principal */
        .main-content {
            flex-grow: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .header {
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        /* Calendario */
        .calendar-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .calendar-header h2 {
            color: #2c3e50;
        }

        .calendar-nav {
            display: flex;
        }

        .calendar-nav button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            margin-left: 5px;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 5px;
        }

        .calendar-day-header {
            text-align: center;
            font-weight: bold;
            padding: 10px 0;
            color: #7f8c8d;
        }

        .calendar-day {
            min-height: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            padding: 5px;
            position: relative;
            border: 1px solid #eee;
        }

        .calendar-day:hover {
            background-color: #e3f2fd;
        }

        .calendar-day.other-month {
            color: #bdc3c7;
            background-color: #f8f9fa;
        }

        .calendar-day.selected {
            background-color: #3498db;
            color: white;
        }

        .calendar-day.today {
            background-color: #e3f2fd;
            font-weight: bold;
        }

        .day-number {
            font-weight: bold;
            margin-bottom: 5px;
            align-self: flex-start;
        }

        .appointment-list {
            width: 100%;
            font-size: 11px;
            overflow-y: auto;
            flex-grow: 1;
        }

        .appointment-item {
            margin-bottom: 3px;
            padding: 2px 4px;
            border-radius: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .legend {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 10px 5px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }

        /* Panel lateral derecho */
        .right-panel {
            width: 300px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-left: 20px;
        }

        .right-panel h2 {
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }

        .section-frame {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }

        .section-frame h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }

        .box-availability {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 4px;
            background-color: white;
            border: 1px solid #eee;
        }

        .box-availability h4 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .turn-availability {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .turn-availability span {
            font-weight: bold;
        }

        .available {
            color: #27ae60;
        }

        .unavailable {
            color: #e74c3c;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background-color: #2ecc71;
            color: white;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transform: translateX(200%);
            transition: transform 0.3s ease-out;
            z-index: 1000;
            max-width: 400px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background-color: #e74c3c;
        }

        /* Modal para conflicto de agendamiento */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .modal-header h3 {
            color: #e74c3c;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #7f8c8d;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
        }

        .modal-footer .btn {
            margin-left: 10px;
            padding: 8px 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Panel lateral izquierdo -->
        <div class="left-panel">
            <h2>Administración</h2>

            <!-- Sección de Boxes -->
            <div class="section">
                <h3>Gestión de Boxes</h3>
                <div class="form-group">
                    <label for="boxName">Nombre del Box</label>
                    <input type="text" id="boxName" placeholder="Ej: Box 1">
                </div>
                <button class="btn" id="addBox">Agregar Box</button>

                <div id="boxesList" style="margin-top: 15px;">
                    <!-- Lista de boxes se generará dinámicamente -->
                </div>
            </div>

            <!-- Sección de Profesionales -->
            <div class="section">
                <h3>Gestión de Profesionales</h3>
                <div class="form-group">
                    <label for="profName">Nombre del Profesional</label>
                    <input type="text" id="profName" placeholder="Ej: Pérez">
                </div>
                <button class="btn" id="addProf">Agregar Profesional</button>

                <div id="profsList" style="margin-top: 15px;">
                    <!-- Lista de profesionales se generará dinámicamente -->
                </div>
            </div>
        </div>

        <!-- Contenido principal -->
        <div class="main-content">
            <div class="header">
                <h1>Sistema de Agendamiento de Turnos</h1>
            </div>

            <!-- Calendario -->
            <div class="calendar-container">
                <div class="calendar-header">
                    <h2 id="currentMonth">Mes Actual</h2>
                    <div class="calendar-nav">
                        <button id="prevMonth">Anterior</button>
                        <button id="nextMonth">Siguiente</button>
                    </div>
                </div>
                <div class="calendar-grid" id="calendarGrid">
                    <!-- Calendario se generará dinámicamente -->
                </div>
                <div class="legend" id="calendarLegend">
                    <!-- Leyenda se generará dinámicamente -->
                </div>
            </div>
        </div>

        <!-- Panel lateral derecho -->
        <div class="right-panel">
            <h2>Disponibilidad de Turnos</h2>

            <div class="section-frame">
                <h3 id="selectedDate">Seleccione una fecha</h3>
                <div id="boxesAvailability">
                    <!-- Disponibilidad de boxes se generará dinámicamente -->
                </div>
            </div>

            <div class="section-frame">
                <h3>Agendar Turno</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="bookingBox">Box</label>
                        <select id="bookingBox">
                            <!-- Opciones se generarán dinámicamente -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bookingTurn">Turno</label>
                        <select id="bookingTurn">
                            <option value="AM">AM</option>
                            <option value="PM">PM</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="bookingProf">Profesional</label>
                    <select id="bookingProf">
                        <!-- Opciones se generarán dinámicamente -->
                    </select>
                </div>
                <button class="btn" id="bookAppointment">Agendar Turno</button>
            </div>
        </div>
    </div>

    <!-- Notificación -->
    <div class="notification" id="notification"></div>

    <!-- Modal para conflicto de agendamiento -->
    <div class="modal" id="conflictModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Conflicto de Agendamiento</h3>
                <button class="modal-close" id="closeModal">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Contenido del modal se generará dinámicamente -->
            </div>
            <div class="modal-footer">
                <button class="btn" id="cancelBooking">Cancelar</button>
            </div>
        </div>
    </div>

    <script>
        // Datos iniciales
        let boxes = [
            { id: 1, name: 'Box 1' },
            { id: 2, name: 'Box 2' }
        ];

        let professionals = [
            { id: 1, name: 'Juan', lastName: 'Pérez' },
            { id: 2, name: 'María', lastName: 'García' }
        ];

        // Estructura para almacenar los turnos agendados
        let appointments = {};

        // Fecha actual y seleccionada
        let currentDate = new Date();
        let selectedDate = null;

        // Colores para los boxes
        const boxColors = [
            '#3498db', // Azul
            '#e74c3c', // Rojo
            '#2ecc71', // Verde
            '#f39c12', // Naranja
            '#9b59b6', // Púrpura
            '#1abc9c', // Turquesa
            '#d35400', // Naranja oscuro
            '#c0392b', // Rojo oscuro
            '#8e44ad', // Púrpura oscuro
            '#27ae60'  // Verde oscuro
        ];

        // Elementos del DOM
        const calendarGrid = document.getElementById('calendarGrid');
        const currentMonthElement = document.getElementById('currentMonth');
        const prevMonthButton = document.getElementById('prevMonth');
        const nextMonthButton = document.getElementById('nextMonth');
        const boxesListElement = document.getElementById('boxesList');
        const profsListElement = document.getElementById('profsList');
        const selectedDateElement = document.getElementById('selectedDate');
        const boxesAvailabilityElement = document.getElementById('boxesAvailability');
        const bookingBoxElement = document.getElementById('bookingBox');
        const bookingProfElement = document.getElementById('bookingProf');
        const notificationElement = document.getElementById('notification');
        const calendarLegendElement = document.getElementById('calendarLegend');
        const conflictModal = document.getElementById('conflictModal');
        const modalBody = document.getElementById('modalBody');
        const closeModalButton = document.getElementById('closeModal');
        const cancelButton = document.getElementById('cancelBooking');
