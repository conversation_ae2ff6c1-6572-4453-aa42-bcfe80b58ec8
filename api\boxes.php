<?php
/**
 * API para manejo de boxes/consultorios
 */

// Iniciar output buffering para evitar output no deseado
ob_start();

require_once '../config.php';

// Configurar headers para CORS y JSON
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$db = getDB();

try {
    switch ($method) {
        case 'GET':
            // Obtener todos los boxes activos
            $stmt = $db->prepare("SELECT id, nombre FROM boxes WHERE activo = 1 ORDER BY nombre");
            $stmt->execute();
            $boxes = $stmt->fetchAll();

            jsonResponse([
                'success' => true,
                'data' => $boxes
            ]);
            break;

        case 'POST':
            // Crear un nuevo box
            $input = json_decode(file_get_contents('php://input'), true);

            if (!isset($input['nombre']) || empty(trim($input['nombre']))) {
                jsonResponse([
                    'success' => false,
                    'message' => 'El nombre del box es requerido'
                ], 400);
            }

            $nombre = trim($input['nombre']);

            // Verificar si ya existe un box con ese nombre
            $stmt = $db->prepare("SELECT id FROM boxes WHERE nombre = ? AND activo = 1");
            $stmt->execute([$nombre]);

            if ($stmt->fetch()) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Ya existe un box con ese nombre'
                ], 400);
            }

            // Insertar el nuevo box
            $stmt = $db->prepare("INSERT INTO boxes (nombre) VALUES (?)");
            $stmt->execute([$nombre]);

            $newId = $db->lastInsertId();

            jsonResponse([
                'success' => true,
                'message' => 'Box creado correctamente',
                'data' => [
                    'id' => $newId,
                    'nombre' => $nombre
                ]
            ]);
            break;

        case 'PUT':
            // Actualizar un box existente
            $input = json_decode(file_get_contents('php://input'), true);

            if (!isset($input['id']) || !isset($input['nombre']) || empty(trim($input['nombre']))) {
                jsonResponse([
                    'success' => false,
                    'message' => 'ID y nombre son requeridos'
                ], 400);
            }

            $id = (int)$input['id'];
            $nombre = trim($input['nombre']);

            // Verificar si el box existe
            $stmt = $db->prepare("SELECT id FROM boxes WHERE id = ? AND activo = 1");
            $stmt->execute([$id]);

            if (!$stmt->fetch()) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Box no encontrado'
                ], 404);
            }

            // Verificar si ya existe otro box con ese nombre
            $stmt = $db->prepare("SELECT id FROM boxes WHERE nombre = ? AND id != ? AND activo = 1");
            $stmt->execute([$nombre, $id]);

            if ($stmt->fetch()) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Ya existe otro box con ese nombre'
                ], 400);
            }

            // Actualizar el box
            $stmt = $db->prepare("UPDATE boxes SET nombre = ? WHERE id = ?");
            $stmt->execute([$nombre, $id]);

            jsonResponse([
                'success' => true,
                'message' => 'Box actualizado correctamente',
                'data' => [
                    'id' => $id,
                    'nombre' => $nombre
                ]
            ]);
            break;

        case 'DELETE':
            // Eliminar un box (soft delete)
            $input = json_decode(file_get_contents('php://input'), true);

            if (!isset($input['id'])) {
                jsonResponse([
                    'success' => false,
                    'message' => 'ID es requerido'
                ], 400);
            }

            $id = (int)$input['id'];

            // Verificar si el box existe
            $stmt = $db->prepare("SELECT id FROM boxes WHERE id = ? AND activo = 1");
            $stmt->execute([$id]);

            if (!$stmt->fetch()) {
                jsonResponse([
                    'success' => false,
                    'message' => 'Box no encontrado'
                ], 404);
            }

            // Verificar si hay turnos asociados al box
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM turnos WHERE box_id = ? AND activo = 1");
            $stmt->execute([$id]);
            $result = $stmt->fetch();

            if ($result['count'] > 0) {
                jsonResponse([
                    'success' => false,
                    'message' => 'No se puede eliminar el box porque tiene turnos asociados'
                ], 400);
            }

            // Realizar soft delete
            $stmt = $db->prepare("UPDATE boxes SET activo = 0 WHERE id = ?");
            $stmt->execute([$id]);

            jsonResponse([
                'success' => true,
                'message' => 'Box eliminado correctamente'
            ]);
            break;

        default:
            jsonResponse([
                'success' => false,
                'message' => 'Método no permitido'
            ], 405);
            break;
    }

} catch (PDOException $e) {
    handleDBError($e, "Error al procesar la solicitud de boxes");
} catch (Exception $e) {
    jsonResponse([
        'success' => false,
        'message' => 'Error interno del servidor'
    ], 500);
}
?>