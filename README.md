# Sistema de Agendamiento de Turnos

Sistema web para gestionar turnos médicos o de consultoría con interfaz intuitiva y base de datos MySQL.

## Características

- **Gestión de Boxes/Consultorios**: Crear, editar y eliminar boxes
- **Gestión de Profesionales**: Administrar lista de profesionales
- **Calendario Visual**: Vista mensual con turnos por colores
- **Agendamiento de Turnos**: Sistema AM/PM con validación de conflictos
- **Base de Datos**: Persistencia en MySQL con APIs REST
- **Responsive**: Interfaz adaptable a diferentes dispositivos

## Requisitos del Sistema

- **Servidor Web**: Apache o Nginx con PHP 7.4+
- **Base de Datos**: MySQL 5.7+ o MariaDB 10.3+
- **PHP Extensions**: PDO, PDO_MySQL
- **Navegador**: Chrome, Firefox, Safari, Edge (versiones recientes)

## Instalación

### 1. Configuración de la Base de Datos

1. Conectarse al servidor MySQL:
```bash
mysql -h ************** -u root -p
# Contraseña: Q7Mc9O9wEbs67fmK5G
```

2. Ejecutar el script de creación de base de datos:
```bash
mysql -h ************** -u root -p < database_setup.sql
```

O copiar y pegar el contenido de `database_setup.sql` en el cliente MySQL.

### 2. Configuración del Servidor Web

1. Subir todos los archivos al directorio web del servidor
2. Asegurar que el servidor web tenga permisos de lectura en todos los archivos
3. Verificar que PHP esté configurado correctamente

### 3. Configuración de la Aplicación

El archivo `config.php` ya está configurado con las credenciales proporcionadas:

```php
define('DB_HOST', '**************');
define('DB_NAME', 'agendamiento');
define('DB_USER', 'root');
define('DB_PASS', 'Q7Mc9O9wEbs67fmK5G');
```

**Importante**: En producción, cambiar `DEBUG_MODE` a `false` en `config.php`.

## Estructura de Archivos

```
proyecto-x/
├── index.php              # Página principal
├── config.php             # Configuración de base de datos
├── database_setup.sql     # Script de creación de BD
├── api/
│   ├── boxes.php          # API para boxes
│   ├── profesionales.php  # API para profesionales
│   └── turnos.php         # API para turnos
├── proyectoX.html         # Archivo HTML original (backup)
└── README.md              # Este archivo
```

## Uso del Sistema

### Gestión de Boxes
1. En el panel izquierdo, ingresar nombre del box
2. Hacer clic en "Agregar Box"
3. Usar botones "Edit" y "Elim" para modificar o eliminar

### Gestión de Profesionales
1. En el panel izquierdo, ingresar nombre completo
2. Hacer clic en "Agregar Profesional"
3. El sistema separará automáticamente nombre y apellido

### Agendamiento de Turnos
1. Seleccionar una fecha en el calendario
2. En el panel derecho, elegir box, turno (AM/PM) y profesional
3. Hacer clic en "Agendar Turno"
4. El sistema validará conflictos automáticamente

## API Endpoints

### Boxes
- `GET api/boxes.php` - Obtener todos los boxes
- `POST api/boxes.php` - Crear nuevo box
- `PUT api/boxes.php` - Actualizar box existente
- `DELETE api/boxes.php` - Eliminar box

### Profesionales
- `GET api/profesionales.php` - Obtener todos los profesionales
- `POST api/profesionales.php` - Crear nuevo profesional
- `PUT api/profesionales.php` - Actualizar profesional
- `DELETE api/profesionales.php` - Eliminar profesional

### Turnos
- `GET api/turnos.php` - Obtener turnos (con filtros por fecha)
- `POST api/turnos.php` - Crear nuevo turno
- `DELETE api/turnos.php` - Eliminar turno

## Solución de Problemas

### Error de Conexión a Base de Datos
1. Verificar credenciales en `config.php`
2. Comprobar que el servidor MySQL esté accesible
3. Verificar que la base de datos `agendamiento` exista

### Errores de JavaScript
1. Abrir herramientas de desarrollador (F12)
2. Revisar la consola para errores específicos
3. Verificar que las APIs respondan correctamente

### Problemas de Permisos
1. Verificar permisos de lectura en archivos PHP
2. Comprobar configuración del servidor web
3. Revisar logs del servidor web

## Seguridad

- Las APIs usan prepared statements para prevenir SQL injection
- Validación de datos en frontend y backend
- Soft delete para mantener integridad referencial
- Headers CORS configurados para desarrollo

**Recomendaciones para Producción**:
- Cambiar `DEBUG_MODE` a `false`
- Usar HTTPS
- Implementar autenticación de usuarios
- Configurar backups automáticos de la base de datos

## Soporte

Para problemas técnicos o consultas sobre el sistema, revisar:
1. Los logs del servidor web
2. Los logs de MySQL
3. La consola del navegador para errores JavaScript